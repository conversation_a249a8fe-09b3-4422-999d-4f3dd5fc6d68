# Achievement System Configuration
# Each achievement has the following properties:
# - id: unique identifier for the achievement
# - name: display name of the achievement
# - description: list of description lines
# - icon: material type for the icon (used by backend servers)
# - threshold: number required to complete the achievement
# - category: bronze, silver, gold, platinum, mythic
# - commands: list of commands to execute when completed (%player% placeholder available)

achievements:
  primeira_picareta:
    id: "primeira_picareta"
    name: "Primeira Picareta"
    description:
      - "Craftaste a tua primeira picareta."
    icon: "WOODEN_PICKAXE"
    threshold: 1
    category: "bronze"
    commands:
      - "eco give %player% 500"

  batismo_de_pedra:
    id: "batismo_de_pedra"
    name: "Batismo de Pedra"
    description:
      - "Partiste 500 blocos."
    icon: "COBBLESTONE"
    threshold: 500
    category: "bronze"
    commands:
      - "eco give %player% 1000"

  minerador_junior:
    id: "minerador_junior"
    name: "Minerador Júnior"
    description:
      - "Concluíste o rank Aprendiz."
    icon: "IRON_PICKAXE"
    threshold: 1
    category: "bronze"
    commands:
      - "eco give %player% 2000"

  maos_sujas:
    id: "maos_sujas"
    name: "Mãos Sujas"
    description:
      - "Recolheste 100 blocos de carvão."
    icon: "COAL"
    threshold: 100
    category: "bronze"
    commands:
      - "eco give %player% 750"

  dia_de_pagamento:
    id: "dia_de_pagamento"
    name: "Dia de Pagamento"
    description:
      - "Vendeste os teus primeiros blocos."
    icon: "EMERALD"
    threshold: 1
    category: "bronze"
    commands:
      - "eco give %player% 500"
