# Achievement System Configuration
# Each achievement has the following properties:
# - id: unique identifier for the achievement
# - name: display name of the achievement
# - description: list of description lines
# - icon: material type for the icon (used by backend servers)
# - threshold: number required to complete the achievement
# - category: bronze, silver, gold, platinum, mythic
# - commands: list of commands to execute when completed (%player% placeholder available)

achievements:
  monster_hunter:
    id: "monster_hunter"
    name: "Monster Hunter"
    description:
      - "Kill 50 hostile mobs."
    icon: "ZOMBIE_HEAD"
    threshold: 50
    category: "bronze"
    commands:
      - "eco give %player% 1000"
  
  first_steps:
    id: "first_steps"
    name: "First Steps"
    description:
      - "Join the server for the first time."
    icon: "GRASS_BLOCK"
    threshold: 1
    category: "bronze"
    commands:
      - "eco give %player% 500"
  
  miner:
    id: "miner"
    name: "Miner"
    description:
      - "Mine 100 blocks."
    icon: "DIAMOND_PICKAXE"
    threshold: 100
    category: "bronze"
    commands:
      - "eco give %player% 1500"
  
  veteran:
    id: "veteran"
    name: "Veteran"
    description:
      - "Play for 10 hours total."
    icon: "CLOCK"
    threshold: 36000
    category: "silver"
    commands:
      - "eco give %player% 5000"
  
  pvp_master:
    id: "pvp_master"
    name: "PvP Master"
    description:
      - "Win 25 PvP battles."
    icon: "DIAMOND_SWORD"
    threshold: 25
    category: "gold"
    commands:
      - "eco give %player% 10000"
