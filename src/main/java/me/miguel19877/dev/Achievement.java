package me.miguel19877.dev;

import java.util.List;
import java.util.Objects;

/**
 * Represents an achievement definition loaded from achievements.yml
 */
public class Achievement {
    
    private final String id;
    private final String name;
    private final List<String> description;
    private final String icon;
    private final int threshold;
    private final String category;
    private final List<String> commands;
    
    public Achievement(String id, String name, List<String> description, String icon, 
                      int threshold, String category, List<String> commands) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.icon = icon;
        this.threshold = threshold;
        this.category = category;
        this.commands = commands;
    }
    
    public String getId() {
        return id;
    }
    
    public String getName() {
        return name;
    }
    
    public List<String> getDescription() {
        return description;
    }
    
    public String getIcon() {
        return icon;
    }
    
    public int getThreshold() {
        return threshold;
    }
    
    public String getCategory() {
        return category;
    }
    
    public List<String> getCommands() {
        return commands;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Achievement that = (Achievement) o;
        return Objects.equals(id, that.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return "Achievement{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", threshold=" + threshold +
                ", category='" + category + '\'' +
                '}';
    }
}
