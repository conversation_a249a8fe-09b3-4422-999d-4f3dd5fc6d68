package me.miguel19877.dev;

import com.velocitypowered.api.event.Subscribe;
import com.velocitypowered.api.event.command.CommandExecuteEvent;
import com.velocitypowered.api.event.connection.DisconnectEvent;
import com.velocitypowered.api.event.connection.PostLoginEvent;
import com.velocitypowered.api.event.player.PlayerChatEvent;
import com.velocitypowered.api.event.player.ServerPreConnectEvent;
import com.velocitypowered.api.proxy.Player;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.serializer.legacy.LegacyComponentSerializer;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

public class LoginListener {

    private final PlayerDataManager playerDataManager;
    private final LegacyComponentSerializer legacySerializer = LegacyComponentSerializer.builder().character('§').build();
    // A list of commands that can be used before logging in
    private static final List<String> ALLOWED_COMMANDS = Arrays.asList("login", "l", "register", "reg", "changepassword", "mudarpassword");
    private static final List<String> PLUGIN_SEE_COMMANDS = Arrays.asList("plugins", "pl", "version", "ver", "help", "h", "?", "about", "bukkit:version", "bukkit:help", "bukkit:?", "bukkit:plugins", "bukkit:pl", "bukkit:ver", "bukkit:about");

    public LoginListener(PlayerDataManager playerDataManager) {
        this.playerDataManager = playerDataManager;
    }

    @Subscribe
    public void onPostLogin(PostLoginEvent event) {
        Player player = event.getPlayer();

        // Start loading the player data asynchronously.
        playerDataManager.loadPlayerData(player.getUniqueId(), player.getUsername())
                .whenComplete((playerData, throwable) -> {
                    if (!player.isActive()) return;

                    if (throwable != null) {
                        player.disconnect(Component.text("An error occurred while loading your data.", NamedTextColor.RED));
                        return;
                    }

                    // Also load achievement data
                    Main.getInstance().getAchievementManager().loadPlayerData(player.getUniqueId())
                            .exceptionally(achievementThrowable -> {
                                // Don't disconnect for achievement loading errors, just log them
                                Main.getInstance().getLogger().error("Failed to load achievement data for player: " + player.getUsername(), achievementThrowable);
                                return null;
                            });

                    if (LoginManager.isRegistered(player.getUniqueId())) {
                        player.sendMessage(legacySerializer.deserialize("§aWelcome back! Please use §e/login <password> §ato log in."));
                    } else {
                        player.sendMessage(legacySerializer.deserialize("§aWelcome! Please use §e/register <password> §ato create an account."));
                    }
                });
    }

    @Subscribe
    public void onPlayerDisconnect(DisconnectEvent event) {
        UUID uuid = event.getPlayer().getUniqueId();

        LoginManager.handlePlayerQuit(uuid);

        // Save both player data and achievement data
        playerDataManager.savePlayerData(uuid)
                .thenRun(() -> playerDataManager.unloadPlayerData(uuid));

        Main.getInstance().getAchievementManager().savePlayerData(uuid)
                .thenRun(() -> Main.getInstance().getAchievementManager().unloadPlayerData(uuid));
    }

    @Subscribe
    public void onPlayerChat(PlayerChatEvent event) {
        Player player = event.getPlayer();
        if (!LoginManager.isLoggedIn(player.getUniqueId())) {
            event.setResult(PlayerChatEvent.ChatResult.denied());
            player.sendMessage(Component.text("You must log in to send messages.", NamedTextColor.RED));
        }
    }

    @Subscribe
    public void onCommandExecute(CommandExecuteEvent event) {
        if (!(event.getCommandSource() instanceof Player)) {
            return; // Not a player
        }
        Player player = (Player) event.getCommandSource();
        String command = event.getCommand().toLowerCase().split(" ")[0]; // Get just the command without arguments

        if (!LoginManager.isLoggedIn(player.getUniqueId()) && !ALLOWED_COMMANDS.contains(command)) {
            event.setResult(CommandExecuteEvent.CommandResult.denied());
            player.sendMessage(Component.text("You must log in to use this command.", NamedTextColor.RED));
        }
        if (PLUGIN_SEE_COMMANDS.contains(command)) {
            event.setResult(CommandExecuteEvent.CommandResult.denied());
            player.sendMessage(Component.text("This server runs Paper with custom plugins made by the CraftAndHelps Dev Team.", NamedTextColor.BLUE));
        }
    }

    @Subscribe
    public void onServerSwitch(ServerPreConnectEvent event) {
        Player player = event.getPlayer();
        // Allow the initial connection to the lobby/auth server
        if (event.getPreviousServer() == null) {
            return;
        }

        if (!LoginManager.isLoggedIn(player.getUniqueId())) {
            event.setResult(ServerPreConnectEvent.ServerResult.denied());
            player.sendMessage(Component.text("You must log in to switch servers.", NamedTextColor.RED));
        }
    }
}