package me.miguel19877.dev;

import com.google.gson.Gson;
import com.google.inject.Inject;
import com.velocitypowered.api.command.CommandManager;
import com.velocitypowered.api.command.CommandMeta;
import com.velocitypowered.api.event.Subscribe;
import com.velocitypowered.api.event.connection.PluginMessageEvent;
import com.velocitypowered.api.event.player.KickedFromServerEvent;
import com.velocitypowered.api.event.player.ServerPostConnectEvent;
import com.velocitypowered.api.event.proxy.ProxyInitializeEvent;
import com.velocitypowered.api.event.proxy.ProxyShutdownEvent;
import com.velocitypowered.api.plugin.Plugin;
import com.velocitypowered.api.proxy.Player;
import com.velocitypowered.api.proxy.ProxyServer;
import com.velocitypowered.api.proxy.messages.MinecraftChannelIdentifier;
import com.velocitypowered.api.proxy.server.RegisteredServer;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer;
import org.slf4j.Logger;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

@Plugin(id = "cahproxy", name = "CAHProxy", version = "1.0", description = "CAH Proxy Plugin", authors = {"Miguel19877"})
public class Main {

    private static Main instance;
    private final Logger logger;
    private final ProxyServer server;
    private final Gson gson = new Gson();

    private DatabaseManager databaseManager;
    private BanManager banManager;
    private PlayerDataManager playerDataManager;
    private VIPManager vipManager;
    private AchievementManager achievementManager;

    private final MinecraftChannelIdentifier channelIdentifier = MinecraftChannelIdentifier.create("cah", "playerdata");
    private final MinecraftChannelIdentifier achievementChannelIdentifier = MinecraftChannelIdentifier.create("ach", "main");

    @Inject
    public Main(Logger logger, ProxyServer server) {
        this.logger = logger;
        this.server = server;
        instance = this;
    }

    @Subscribe
    public void onProxyInitialization(ProxyInitializeEvent event) {
        server.getChannelRegistrar().register(channelIdentifier);
        server.getChannelRegistrar().register(achievementChannelIdentifier);

        // Initialize managers
        databaseManager = new DatabaseManager();
        banManager = new BanManager(databaseManager);
        playerDataManager = new PlayerDataManager(databaseManager, logger);
        vipManager = new VIPManager(databaseManager, playerDataManager, logger);
        achievementManager = new AchievementManager(databaseManager, logger);

        // Register Listeners
        server.getEventManager().register(this, new BanListener(banManager));
        server.getEventManager().register(this, new LoginListener(playerDataManager)); // Centralized login/quit logic
        server.getEventManager().register(this, new VIPListener(vipManager, server, logger));

        // Register Commands
        CommandManager commandManager = server.getCommandManager();
        commandManager.register(commandManager.metaBuilder("banir").build(), new BanCommand(this, banManager, server));
        commandManager.register(commandManager.metaBuilder("desbanir").build(), new UnbanCommand(this, banManager));
        commandManager.register(commandManager.metaBuilder("mutar").build(), new MuteCommand(this, banManager, server));
        commandManager.register(commandManager.metaBuilder("desmutar").build(), new UnmuteCommand(this, banManager));
        commandManager.register(commandManager.metaBuilder("kick").build(), new KickCommand(this, server));
        commandManager.register(commandManager.metaBuilder("login").aliases("l").build(), new LoginCommand());
        commandManager.register(commandManager.metaBuilder("register").aliases("reg").build(), new RegisterCommand());
        commandManager.register(commandManager.metaBuilder("changepassword").aliases("mudarpassword").build(), new ChangePasswordCommand());
        commandManager.register(commandManager.metaBuilder("idioma").aliases("language").build(), new LanguageCommand(this));
        commandManager.register(commandManager.metaBuilder("hub").build(), new HubCommand());
        commandManager.register(commandManager.metaBuilder("vip").build(), new VIPCommand(vipManager, server));

        logger.info("CAHProxy Plugin has started!");
    }

    @Subscribe
    public void onProxyShutdown(ProxyShutdownEvent event) {
        server.getChannelRegistrar().unregister(channelIdentifier);
        server.getChannelRegistrar().unregister(achievementChannelIdentifier);
        if (databaseManager != null) {
            databaseManager.close();
        }
        logger.info("CAHProxy Plugin has stopped!");
    }

    @Subscribe
    public void onServerPostConnect(ServerPostConnectEvent event) {
        Player player = event.getPlayer();

        // ServerPostConnectEvent is fired for every server switch,
        // including the initial login.
        // player.getCurrentServer() will always be present here.
        player.getCurrentServer().ifPresent(serverConnection -> {
            RegisteredServer server = serverConnection.getServer();
            PlayerData data = playerDataManager.getPlayerData(player.getUniqueId());

            if (data != null) {
                // No delay is needed here, as this event ensures the player
                // is fully connected on the backend.
                String json = gson.toJson(data);
                byte[] messageBytes = json.getBytes(StandardCharsets.UTF_8);

                // Send the player data message
                serverConnection.sendPluginMessage(channelIdentifier, messageBytes);
                logger.info("Sent data for player {} to server {}.", player.getUsername(), server.getServerInfo().getName());

                // Send achievement data
                sendAchievementDataToServer(player, serverConnection);

            } else {
                logger.warn("Could not find cached data for {} when connecting to {}. Data might not have loaded yet.", player.getUsername(), server.getServerInfo().getName());
            }
        });
    }

    @Subscribe
    public void onKickedFromServer(KickedFromServerEvent event) {
        Player player = event.getPlayer();
        Optional<Component> reasonComponent = event.getServerKickReason();

        // Check if a reason was provided
        if (reasonComponent.isPresent()) {
            // Convert the Component reason to a plain text string to check it
            String reasonString = PlainTextComponentSerializer.plainText().serialize(reasonComponent.get());

            // Check if the reason is "Server closed"
            if (reasonString.equalsIgnoreCase("Server closed")) {
                // Get the lobby server from your velocity.toml
                Optional<RegisteredServer> lobby = Main.getInstance().getServer().getServer("lobby");

                if (lobby.isPresent()) {
                    // Prevent a redirect loop if they were already on the lobby
                    if (event.getServer().getServerInfo().getName().equalsIgnoreCase("lobby")) {
                        // If they were kicked from the lobby itself, let the default behavior happen
                        return;
                    }

                    // Set the result of the event to redirect the player to the lobby server
                    KickedFromServerEvent.RedirectPlayer redirect = (KickedFromServerEvent.RedirectPlayer) KickedFromServerEvent.RedirectPlayer.create(lobby.get());
                    event.setResult(redirect);

                }
            }
        }
    }

    // --- NEW: Listen for plugin messages from backend servers (deltas) ---
    @Subscribe
    public void onPluginMessage(PluginMessageEvent event) {
        if (event.getIdentifier().equals(channelIdentifier)) {
            handlePlayerDataMessage(event);
        } else if (event.getIdentifier().equals(achievementChannelIdentifier)) {
            handleAchievementMessage(event);
        }
    }

    private void handlePlayerDataMessage(PluginMessageEvent event) {
        byte[] data = event.getData();
        String json = new String(data, StandardCharsets.UTF_8);

        try {
            PlayerDataDelta delta = gson.fromJson(json, PlayerDataDelta.class);
            if (delta == null || delta.getUuid() == null) return;
            logger.info("Received step 4 - " + delta.getUuid());
            PlayerData playerData = playerDataManager.getPlayerData(delta.getUuid());
            if (playerData != null) {
                playerData.applyDelta(delta);
                logger.info("Applied delta for player {}: {}", playerData.getUsername(), json);
            }
        } catch (Exception e) {
            logger.error("Failed to process player data delta: {}", json, e);
        }
    }

    private void handleAchievementMessage(PluginMessageEvent event) {
        byte[] data = event.getData();
        String json = new String(data, StandardCharsets.UTF_8);

        try {
            achievementManager.handleAchievementUpdate(json);

            // Send updated achievement data back to the player's current server
            @SuppressWarnings("unchecked")
            Map<String, Object> updateData = gson.fromJson(json, Map.class);
            String playerIdStr = (String) updateData.get("playerId");

            if (playerIdStr != null) {
                UUID playerId = UUID.fromString(playerIdStr);
                Optional<Player> player = server.getPlayer(playerId);

                if (player.isPresent()) {
                    player.get().getCurrentServer().ifPresent(serverConnection -> {
                        sendAchievementDataToServer(player.get(), serverConnection);
                    });
                }
            }

        } catch (Exception e) {
            logger.error("Failed to process achievement update: {}", json, e);
        }
    }

    public static Main getInstance() {
        return instance;
    }

    public BanManager getBanManager() {
        return banManager;
    }

    public DatabaseManager getDatabaseManager() {
        return databaseManager;
    }

    public PlayerDataManager getPlayerDataManager() {
        return playerDataManager;
    }

    public VIPManager getVipManager() {
        return vipManager;
    }

    public AchievementManager getAchievementManager() {
        return achievementManager;
    }

    public ProxyServer getServer() {
        return server;
    }

    /**
     * Send achievement data to a backend server for a specific player
     */
    private void sendAchievementDataToServer(Player player, com.velocitypowered.api.proxy.ServerConnection serverConnection) {
        // Load achievement data if not already cached
        AchievementPlayerData playerAchievementData = achievementManager.getPlayerData(player.getUniqueId());

        if (playerAchievementData == null) {
            // Load achievement data asynchronously
            achievementManager.loadPlayerData(player.getUniqueId()).thenAccept(data -> {
                if (data != null) {
                    sendAchievementInitMessage(player, serverConnection, data);
                }
            }).exceptionally(throwable -> {
                logger.error("Failed to load achievement data for player: " + player.getUsername(), throwable);
                return null;
            });
        } else {
            sendAchievementInitMessage(player, serverConnection, playerAchievementData);
        }
    }

    /**
     * Send INIT message with achievement definitions and player progress
     */
    private void sendAchievementInitMessage(Player player, com.velocitypowered.api.proxy.ServerConnection serverConnection, AchievementPlayerData playerData) {
        try {
            Map<String, Object> initData = new HashMap<>();
            initData.put("subChannel", "INIT");
            initData.put("playerId", player.getUniqueId().toString());
            initData.put("achievements", achievementManager.getAchievements());
            initData.put("playerData", playerData);

            String json = gson.toJson(initData);
            byte[] messageBytes = json.getBytes(StandardCharsets.UTF_8);

            serverConnection.sendPluginMessage(achievementChannelIdentifier, messageBytes);
            logger.debug("Sent achievement INIT data for player {} to server {}",
                        player.getUsername(), serverConnection.getServerInfo().getName());

        } catch (Exception e) {
            logger.error("Failed to send achievement INIT data for player: " + player.getUsername(), e);
        }
    }

}