package me.miguel19877.dev;

import com.google.gson.Gson;
import org.slf4j.Logger;
import org.yaml.snakeyaml.Yaml;

import java.io.InputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Manages the achievement system on the Velocity proxy side
 */
public class AchievementManager {
    
    private final DatabaseManager databaseManager;
    private final Logger logger;
    private final Gson gson;
    
    // Achievement definitions loaded from achievements.yml
    private final Map<String, Achievement> achievements = new ConcurrentHashMap<>();
    
    // Player achievement data cache: UUID -> AchievementPlayerData
    private final Map<UUID, AchievementPlayerData> playerDataCache = new ConcurrentHashMap<>();
    
    public AchievementManager(DatabaseManager databaseManager, Logger logger) {
        this.databaseManager = databaseManager;
        this.logger = logger;
        this.gson = new Gson();
        
        // Load achievement definitions
        loadAchievements();
        
        logger.info("AchievementManager initialized with {} achievements", achievements.size());
    }
    
    /**
     * Load achievement definitions from achievements.yml
     */
    private void loadAchievements() {
        try {
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream("achievements.yml");
            if (inputStream == null) {
                logger.error("Could not find achievements.yml in resources!");
                return;
            }
            
            Yaml yaml = new Yaml();
            Map<String, Object> data = yaml.load(inputStream);
            
            @SuppressWarnings("unchecked")
            Map<String, Map<String, Object>> achievementsData = (Map<String, Map<String, Object>>) data.get("achievements");
            
            if (achievementsData == null) {
                logger.warn("No achievements found in achievements.yml");
                return;
            }
            
            for (Map.Entry<String, Map<String, Object>> entry : achievementsData.entrySet()) {
                String key = entry.getKey();
                Map<String, Object> achievementData = entry.getValue();
                
                try {
                    Achievement achievement = parseAchievement(achievementData);
                    achievements.put(achievement.getId(), achievement);
                    logger.debug("Loaded achievement: {}", achievement.getId());
                } catch (Exception e) {
                    logger.error("Failed to parse achievement: {}", key, e);
                }
            }
            
            logger.info("Loaded {} achievements from achievements.yml", achievements.size());
            
        } catch (Exception e) {
            logger.error("Failed to load achievements.yml", e);
        }
    }

    /**
     * Parse achievement data from YAML
     */
    @SuppressWarnings("unchecked")
    private Achievement parseAchievement(Map<String, Object> data) {
        String id = (String) data.get("id");
        String name = (String) data.get("name");
        List<String> description = (List<String>) data.get("description");
        String icon = (String) data.get("icon");
        Integer threshold = (Integer) data.get("threshold");
        String category = (String) data.get("category");
        List<String> commands = (List<String>) data.get("commands");

        if (id == null || name == null || threshold == null) {
            throw new IllegalArgumentException("Achievement missing required fields: id, name, or threshold");
        }

        return new Achievement(id, name,
                              description != null ? description : new ArrayList<>(),
                              icon != null ? icon : "PAPER",
                              threshold,
                              category != null ? category : "bronze",
                              commands != null ? commands : new ArrayList<>());
    }

    /**
     * Load player achievement data from database
     */
    public CompletableFuture<AchievementPlayerData> loadPlayerData(UUID playerId) {
        return CompletableFuture.supplyAsync(() -> {
            try (Connection conn = databaseManager.getConnection()) {
                AchievementPlayerData playerData = new AchievementPlayerData(playerId);

                // Load progress
                String progressSQL = "SELECT achievement_id, progress FROM achievement_progress WHERE player_uuid = ?";
                try (PreparedStatement stmt = conn.prepareStatement(progressSQL)) {
                    stmt.setString(1, playerId.toString());
                    ResultSet rs = stmt.executeQuery();

                    while (rs.next()) {
                        String achievementId = rs.getString("achievement_id");
                        int progress = rs.getInt("progress");
                        playerData.setProgress(achievementId, progress);
                    }
                }

                // Load completions
                String completionsSQL = "SELECT achievement_id, completed_at FROM achievement_completions WHERE player_uuid = ?";
                try (PreparedStatement stmt = conn.prepareStatement(completionsSQL)) {
                    stmt.setString(1, playerId.toString());
                    ResultSet rs = stmt.executeQuery();

                    while (rs.next()) {
                        String achievementId = rs.getString("achievement_id");
                        long completedAt = rs.getLong("completed_at");
                        playerData.markCompleted(achievementId, completedAt);
                    }
                }

                // Cache the data
                playerDataCache.put(playerId, playerData);
                logger.debug("Loaded achievement data for player: {}", playerId);

                return playerData;

            } catch (SQLException e) {
                logger.error("Failed to load achievement data for player: " + playerId, e);
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * Save player achievement data to database
     */
    public CompletableFuture<Void> savePlayerData(UUID playerId) {
        return CompletableFuture.runAsync(() -> {
            AchievementPlayerData playerData = playerDataCache.get(playerId);
            if (playerData == null) {
                logger.warn("Attempted to save achievement data for player not in cache: {}", playerId);
                return;
            }

            try (Connection conn = databaseManager.getConnection()) {
                // Save progress
                String progressSQL = "INSERT INTO achievement_progress (player_uuid, achievement_id, progress) " +
                                   "VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE progress = VALUES(progress)";

                try (PreparedStatement stmt = conn.prepareStatement(progressSQL)) {
                    for (Map.Entry<String, Integer> entry : playerData.getProgress().entrySet()) {
                        stmt.setString(1, playerId.toString());
                        stmt.setString(2, entry.getKey());
                        stmt.setInt(3, entry.getValue());
                        stmt.addBatch();
                    }
                    stmt.executeBatch();
                }

                // Save completions
                String completionsSQL = "INSERT INTO achievement_completions (player_uuid, achievement_id, completed_at) " +
                                      "VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE completed_at = VALUES(completed_at)";

                try (PreparedStatement stmt = conn.prepareStatement(completionsSQL)) {
                    for (Map.Entry<String, Long> entry : playerData.getCompletions().entrySet()) {
                        stmt.setString(1, playerId.toString());
                        stmt.setString(2, entry.getKey());
                        stmt.setLong(3, entry.getValue());
                        stmt.addBatch();
                    }
                    stmt.executeBatch();
                }

                logger.debug("Saved achievement data for player: {}", playerId);

            } catch (SQLException e) {
                logger.error("Failed to save achievement data for player: " + playerId, e);
            }
        });
    }

    /**
     * Update player progress from backend server
     */
    public void updatePlayerProgress(UUID playerId, String achievementId, int newProgress) {
        AchievementPlayerData playerData = playerDataCache.get(playerId);
        if (playerData == null) {
            logger.warn("Received progress update for player not in cache: {}", playerId);
            return;
        }

        Achievement achievement = achievements.get(achievementId);
        if (achievement == null) {
            logger.warn("Received progress update for unknown achievement: {}", achievementId);
            return;
        }

        // Update progress
        int oldProgress = playerData.getProgress(achievementId);
        playerData.setProgress(achievementId, newProgress);

        // Check if achievement was completed
        if (!playerData.isCompleted(achievementId) && newProgress >= achievement.getThreshold()) {
            playerData.markCompleted(achievementId);
            logger.info("Player {} completed achievement: {}", playerId, achievementId);
        }

        // Save to database
        savePlayerData(playerId);

        logger.debug("Updated progress for player {} achievement {}: {} -> {}",
                    playerId, achievementId, oldProgress, newProgress);
    }

    /**
     * Get player achievement data from cache
     */
    public AchievementPlayerData getPlayerData(UUID playerId) {
        return playerDataCache.get(playerId);
    }

    /**
     * Remove player data from cache
     */
    public void unloadPlayerData(UUID playerId) {
        playerDataCache.remove(playerId);
    }

    /**
     * Get all achievement definitions
     */
    public Map<String, Achievement> getAchievements() {
        return new HashMap<>(achievements);
    }

    /**
     * Get a specific achievement by ID
     */
    public Achievement getAchievement(String achievementId) {
        return achievements.get(achievementId);
    }

    /**
     * Create serialized achievement data for sending to backend servers
     */
    public String serializeAchievementData(UUID playerId) {
        AchievementPlayerData playerData = playerDataCache.get(playerId);
        if (playerData == null) {
            return "{}";
        }

        Map<String, Object> data = new HashMap<>();
        data.put("achievements", achievements);
        data.put("playerData", playerData);

        return gson.toJson(data);
    }

    /**
     * Parse achievement update message from backend server
     */
    public void handleAchievementUpdate(String jsonData) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> data = gson.fromJson(jsonData, Map.class);

            String playerIdStr = (String) data.get("playerId");
            String achievementId = (String) data.get("achievementId");
            Double progressDouble = (Double) data.get("progress");

            if (playerIdStr == null || achievementId == null || progressDouble == null) {
                logger.warn("Invalid achievement update data received: {}", jsonData);
                return;
            }

            UUID playerId = UUID.fromString(playerIdStr);
            int progress = progressDouble.intValue();

            updatePlayerProgress(playerId, achievementId, progress);

        } catch (Exception e) {
            logger.error("Failed to parse achievement update: {}", jsonData, e);
        }
    }
}
